// Snap Dashboard JS - Merged version

// HTML structure as a string (from snapapp.html)
const snapAppHTML = `
<div class="snapapp-container">
  <aside class="sidebar">
    <div class="sidebar-logo">
      <img src="./assets/snap-logo.png" alt="Snap Logo" class="sidebar-logo-icon" style="width:48px;height:48px;">
      <img src="./assets/logo-wordmark.svg" alt="Snap for MOD Logo" class="wordmark" style="max-width: 60px; height: auto;">
      <button class="sidebar-collapse-btn" aria-label="Collapse sidebar">
        <img src="./assets/collapse-ic.svg" alt="Collapse Sidebar" class="collapse-icon">
      </button>
    </div>
    <nav class="sidebar-nav">
      <button class="sidebar-btn active" data-label="Dashboard"><img src="./assets/dashboard-active-ic.svg" alt="Dashboard" class="sidebar-icon"><span>Dashboard</span></button>
      <button class="sidebar-btn" data-label="Snap Image Studio"><img src="./assets/snap-image-ic.svg" alt="Snap Image Studio" class="sidebar-icon"><span>Snap Image Studio</span></button>
      <button class="sidebar-btn locked" data-label="Automate"><img src="./assets/automate-disabled-ic.svg" alt="Automate" class="sidebar-icon"><span>Automate</span><div class="lock-icon-container"><img src="./assets/lock-ic.svg" alt="Locked" class="lock-icon"></div><div class="tooltip"><img src="./assets/dev-ic.svg" alt="Development Icon">Development in Progress</div></button>
      <button class="sidebar-btn locked" data-label="Analytics"><img src="./assets/analytics-disabled-ic.svg" alt="Analytics" class="sidebar-icon"><span>Analytics</span><div class="lock-icon-container"><img src="./assets/lock-ic.svg" alt="Locked" class="lock-icon"></div><div class="tooltip"><img src="./assets/dev-ic.svg" alt="Development Icon">Development in Progress</div></button>
      <button class="sidebar-btn locked" data-label="Hammer"><img src="./assets/hammer-disabled-ic.svg" alt="Hammer" class="sidebar-icon"><span>Hammer</span><div class="lock-icon-container"><img src="./assets/lock-ic.svg" alt="Locked" class="lock-icon"></div><div class="tooltip"><img src="./assets/dev-ic.svg" alt="Development Icon">Development in Progress</div></button>
      <button class="sidebar-btn locked" data-label="Products"><img src="./assets/products-disabled-ic.svg" alt="Products" class="sidebar-icon"><span>Products</span><div class="lock-icon-container"><img src="./assets/lock-ic.svg" alt="Locked" class="lock-icon"></div><div class="tooltip"><img src="./assets/dev-ic.svg" alt="Development Icon">Development in Progress</div></button>
      <button class="sidebar-btn" data-label="Settings"><img src="./assets/settings-ic.svg" alt="Settings" class="sidebar-icon"><span>Settings</span></button>
    </nav>
    <div class="theme-toggle-container">
      <button class="theme-toggle-btn" aria-label="Toggle theme" data-tooltip="Switch to Dark Mode">
        <div class="toggle">
          <div class="theme-icon">
            <img src="./assets/dark-mode-ic.svg" alt="Dark mode" />
          </div>
        </div>
        <div class="toggle active">
          <div class="theme-icon">
            <img src="./assets/light-mode-ic.svg" alt="Light mode" />
          </div>
        </div>
      </button>
    </div>
  </aside>
  <main class="main-content">
  </main>
</div>
`;

// Component Registry - maps button data-labels to folder names and file names
const componentMap = {
  'Dashboard': 'dashboard',
  'Snap Image Studio': 'snap-image-studio',
  'Automate': 'automate',
  'Analytics': 'analytics',
  'Hammer': 'hammer',
  'Products': 'products',
  'Settings': 'settings'
};

// Global variable to track current component for proper lifecycle management
let currentComponent = null;

// Main content container reference - will be set after DOM creation
let mainContent;

// Global checkbox state management
function initializeCheckboxes() {
  // Use event delegation on document body for better performance
  document.body.addEventListener('click', (e) => {
    // Find the closest checkbox wrapper (if any)
    const checkboxWrapper = e.target.closest('.checkbox-wrapper');
    if (!checkboxWrapper) return;

    // Get the checkbox icon and label
    const checkboxIcon = checkboxWrapper.querySelector('.checkbox-icon');
    if (!checkboxIcon) return;

    // Toggle checkbox state
    const isChecked = checkboxIcon.src.includes('checkbox-ic.svg');
    const newState = !isChecked;
    
    // Update icon
    checkboxIcon.src = `./assets/${newState ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
    
    // Dispatch custom event for components to listen to
    const event = new CustomEvent('checkbox-change', {
      detail: {
        checked: newState,
        wrapper: checkboxWrapper,
        id: checkboxWrapper.id || null
      }
    });
    checkboxWrapper.dispatchEvent(event);
  });
}

// Function to update active navigation
function updateNavigation(activeBtn) {
    if (!activeBtn) return; // Early return if no button provided
    
    const allBtns = document.querySelectorAll('.sidebar-btn');
    allBtns.forEach(btn => {
        if (btn === activeBtn && !btn.classList.contains('locked')) {
            if (!btn.classList.contains('active')) {
                btn.classList.add('active');
                updateButtonIcon(btn, true);
            }
        } else {
            btn.classList.remove('active');
            updateButtonIcon(btn, false);
        }
    });
}

// Function to update button icon - now with caching
const iconCache = new Map();
function updateButtonIcon(btn, isActive) {
    const iconImg = btn.querySelector('.sidebar-icon');
    if (!iconImg) return;
    
    // Get the component name from data-label
    let componentName = btn.getAttribute('data-label').toLowerCase().replace(/\s+/g, '-');
    
    // Special case for "Snap Image Studio" which uses "snap-image" in asset filenames
    if (componentName === 'snap-image-studio') {
        componentName = 'snap-image';
    }
    
    // Generate cache key
    const cacheKey = `${componentName}-${isActive}-${btn.classList.contains('locked')}`;
    
    // Check cache first
    if (iconCache.has(cacheKey)) {
        iconImg.src = iconCache.get(cacheKey);
        return;
    }
    
    // Generate the icon path based on state
    let iconPath;
    if (btn.classList.contains('locked') || btn.disabled) {
        iconPath = `./assets/${componentName}-disabled-ic.svg`;
    } else if (isActive) {
        iconPath = `./assets/${componentName}-active-ic.svg`;
    } else {
        iconPath = `./assets/${componentName}-ic.svg`;
    }
    
    // Cache the path
    iconCache.set(cacheKey, iconPath);
    
    // Set the icon source
    iconImg.src = iconPath;
}

// Component cache
const componentCache = {};

// Global Scroll-Aware Tooltip Guard
(function initGlobalTooltipScrollGuard() {
  if (window.GlobalTooltipScrollGuard) return;

  const SUPPRESS_AFTER_SCROLL_MS = 0; // no delay; allow immediate hover after scroll ends
  let suppressUntilTs = 0;
  let suppressTimer = null;

  function isSuppressed() {
    return Date.now() < suppressUntilTs;
  }

  function scheduleRelease() {
    if (suppressTimer) clearTimeout(suppressTimer);
    const remaining = Math.max(0, suppressUntilTs - Date.now());
    suppressTimer = setTimeout(() => {
      if (!isSuppressed()) {
        document.documentElement.removeAttribute('data-scroll-active');
        window.dispatchEvent(new CustomEvent('global:tooltips-allow'));
        suppressTimer = null;
      } else {
        scheduleRelease();
      }
    }, remaining + 10);
  }

  function onAnyScroll() {
    suppressUntilTs = Date.now() + SUPPRESS_AFTER_SCROLL_MS;
    document.documentElement.setAttribute('data-scroll-active', 'true');
    // Signal all tooltip systems to hide immediately
    window.dispatchEvent(new CustomEvent('global:tooltips-hide'));
    scheduleRelease();
  }

  // Attach broad set of listeners covering wheel/touch/trackpad momentum
  window.addEventListener('scroll', onAnyScroll, { passive: true });
  window.addEventListener('wheel', onAnyScroll, { passive: true });
  window.addEventListener('touchmove', onAnyScroll, { passive: true });

  // Expose guard API
  window.GlobalTooltipScrollGuard = {
    isSuppressed,
    // optional external suppression (e.g., programmatic scrolls)
    suppressFor(ms) {
      suppressUntilTs = Date.now() + Math.max(0, ms || SUPPRESS_AFTER_SCROLL_MS);
      onAnyScroll();
    }
  };
})();

// Global Tooltip System - JavaScript-based approach like marketplace tooltips
(function initGlobalTooltipSystem() {
  let isInitialized = false;
  let tooltipContainer = null;
  const activeTooltips = new Map(); // Track active tooltips for each element
  let pendingHover = null; // Cache hover while scrolling is active
  
  function initTooltipSystem() {
    if (isInitialized) return;
    isInitialized = true;
    
    // Create a global tooltip container (like marketplace tooltips)
    tooltipContainer = document.getElementById('global-tooltip-container');
    if (!tooltipContainer) {
      tooltipContainer = document.createElement('div');
      tooltipContainer.id = 'global-tooltip-container';
      tooltipContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 999999;
      `;
      document.body.appendChild(tooltipContainer);
    }
    
    // Disable CSS-based tooltips completely
    const disableCSS = document.createElement('style');
    disableCSS.id = 'disable-css-tooltips';
    disableCSS.textContent = `
      /* Disable all CSS-based tooltips */
      [data-tooltip]:before,
      [data-tooltip]:after {
        display: none !important;
      }
    `;
    document.head.appendChild(disableCSS);
  }
  
     function createCustomTooltip(element, tooltipText) {
     // Create custom tooltip element (like marketplace approach)
     const customTooltip = document.createElement('div');
     customTooltip.className = 'global-custom-tooltip';
     
     // Create text node separately for easier updates
     const textNode = document.createTextNode(tooltipText);
     customTooltip.appendChild(textNode);
     
     customTooltip.style.cssText = `
       position: absolute;
       background: #000000;
       color: #FFFFFF;
       padding: 8px 16px;
       border-radius: 6px;
       font-family: 'Amazon Ember', sans-serif;
       font-size: 12px;
       font-weight: 500;
       white-space: nowrap;
       opacity: 0;
       visibility: hidden;
       transition: opacity 0.2s ease, visibility 0.2s ease;
       z-index: 999999;
       pointer-events: none;
       width: auto;
       min-width: fit-content;
       max-width: 300px;
       text-align: center;
       box-sizing: border-box;
     `;

    // Create arrow element
    const arrow = document.createElement('div');
    arrow.className = 'global-tooltip-arrow';
    arrow.style.cssText = `
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid #000000;
    `;
    customTooltip.appendChild(arrow);
    tooltipContainer.appendChild(customTooltip);
    
    // Update theme - now unified to black background
    const updateTheme = () => {
      customTooltip.style.background = '#000000';
      customTooltip.style.color = '#FFFFFF';
      arrow.style.borderTopColor = '#000000';
    };
    updateTheme();
    
    return { tooltip: customTooltip, updateTheme };
  }
  
     function showTooltip(element, tooltipText) {
      // Suppress while scrolling
      if (window.GlobalTooltipScrollGuard && window.GlobalTooltipScrollGuard.isSuppressed()) return;
     if (!tooltipContainer) return;
     
     // Create tooltip if it doesn't exist
     if (!activeTooltips.has(element)) {
       const tooltipData = createCustomTooltip(element, tooltipText);
       activeTooltips.set(element, tooltipData);
     }
     
     const { tooltip, updateTheme } = activeTooltips.get(element);
     updateTheme(); // Update theme before showing
     
     // Update tooltip text in case it changed
     tooltip.firstChild.nodeValue = tooltipText;
     
     // Make tooltip temporarily visible off-screen to measure its natural width
     tooltip.style.visibility = 'hidden';
     tooltip.style.opacity = '1';
     tooltip.style.left = '-9999px';
     tooltip.style.top = '-9999px';
     tooltip.style.width = 'auto';
     tooltip.style.whiteSpace = 'nowrap';
     
     // Force a reflow to get accurate measurements
     tooltip.offsetWidth;
     
     // Get the natural width of the tooltip
     const tooltipRect = tooltip.getBoundingClientRect();
     const naturalWidth = tooltipRect.width;
     
     // Position tooltip above the element, centered
     const rect = element.getBoundingClientRect();
     
     const left = rect.left + (rect.width / 2) - (naturalWidth / 2);
     const top = rect.top - tooltipRect.height - 8;
     
     // Keep tooltip within viewport bounds
     const finalLeft = Math.max(10, Math.min(left, window.innerWidth - naturalWidth - 10));
     const finalTop = Math.max(10, top);
     
     // Apply final positioning and show tooltip
     tooltip.style.left = `${finalLeft}px`;
     tooltip.style.top = `${finalTop}px`;
     tooltip.style.visibility = 'visible';
   }
  
  function hideTooltip(element) {
    if (!activeTooltips.has(element)) return;
    
    const { tooltip } = activeTooltips.get(element);
    tooltip.style.opacity = '0';
    tooltip.style.visibility = 'hidden';
  }
  
  // Helper function to safely find closest element, handling non-Element nodes
  function safeClosest(target, selector) {
    // If target is not an Element, traverse up to find the nearest Element
    let element = target;
    while (element && element.nodeType !== Node.ELEMENT_NODE) {
      element = element.parentNode;
    }
    
    // If we found an Element, use closest(); otherwise return null
    return element && typeof element.closest === 'function' ? element.closest(selector) : null;
  }

   function handleMouseEnter(e) {
     // Determine element with tooltip
    const tooltipElement = safeClosest(e.target, '[data-tooltip]');
    if (!tooltipElement) return;
     const tooltipText = tooltipElement.getAttribute('data-tooltip');
     if (!tooltipText) return;

     // Suppress while scrolling (prevents showing during active scroll) — queue hover intent
     if (window.GlobalTooltipScrollGuard && window.GlobalTooltipScrollGuard.isSuppressed()) {
       pendingHover = { element: tooltipElement, text: tooltipText };
       return;
     }
    
    // Skip marketplace columns as they have their own custom system
    if (tooltipElement.classList.contains('marketplace-col')) return;
    
    // Skip elements that have tooltips explicitly disabled
    if (tooltipElement.hasAttribute('data-no-tooltip')) return;
    
    // Skip if hovering over dropdown menu items or open dropdown areas
    if (safeClosest(e.target, '.dropdown-menu, .dropdown-item')) return;
    if (safeClosest(e.target, '.snap-dropdown.focused')) return;
    
    // Temporarily remove data-tooltip to prevent CSS tooltip
    tooltipElement.removeAttribute('data-tooltip');
    tooltipElement.setAttribute('data-tooltip-temp', tooltipText);
    
    showTooltip(tooltipElement, tooltipText);
  }
  
   function handleMouseLeave(e) {
    const tooltipElement = safeClosest(e.target, '[data-tooltip], [data-tooltip-temp]');
    if (!tooltipElement) return;
    
    // Skip marketplace columns
    if (tooltipElement.classList.contains('marketplace-col')) return;
    
    // Restore data-tooltip attribute
    const tooltipText = tooltipElement.getAttribute('data-tooltip-temp');
    if (tooltipText) {
      tooltipElement.setAttribute('data-tooltip', tooltipText);
      tooltipElement.removeAttribute('data-tooltip-temp');
    }
    
    hideTooltip(tooltipElement);
     // Clear pending hover if it's the same element
     if (pendingHover && pendingHover.element === tooltipElement) {
       pendingHover = null;
     }
  }
  
  function handleClick(e) {
    const tooltipElement = safeClosest(e.target, '[data-tooltip], [data-tooltip-temp]');
    if (!tooltipElement) return;
    
    // Skip marketplace columns
    if (tooltipElement.classList.contains('marketplace-col')) return;
    
    hideTooltip(tooltipElement);
  }
  
  function attachTooltipListeners() {
    // Remove existing listeners to prevent duplicates
    document.removeEventListener('mouseenter', handleMouseEnter, true);
    document.removeEventListener('mouseleave', handleMouseLeave, true);
    document.removeEventListener('click', handleClick, true);
    
    // Add new listeners with capture=true to catch all elements
    document.addEventListener('mouseenter', handleMouseEnter, true);
    document.addEventListener('mouseleave', handleMouseLeave, true);
    document.addEventListener('click', handleClick, true);
  }
  
  // Track current mouse position
  let currentMouseX = 0;
  let currentMouseY = 0;
  
  function updateMousePosition(e) {
    currentMouseX = e.clientX;
    currentMouseY = e.clientY;
  }
  
  function isElementUnderMouse(element) {
    const rect = element.getBoundingClientRect();
    // Add a small tolerance (1px) to handle edge cases
    const tolerance = 1;
    return (
      currentMouseX >= rect.left - tolerance &&
      currentMouseX <= rect.right + tolerance &&
      currentMouseY >= rect.top - tolerance &&
      currentMouseY <= rect.bottom + tolerance
    );
  }
  
   function updateTooltipPositions() {
     // If globally suppressed, hide all visible custom tooltips quickly
     if (window.GlobalTooltipScrollGuard && window.GlobalTooltipScrollGuard.isSuppressed()) {
       activeTooltips.forEach((_, element) => hideTooltip(element));
       return;
     }
    // Check all visible tooltips and hide those no longer being hovered
    activeTooltips.forEach(({ tooltip }, element) => {
      if (tooltip.style.opacity === '1') {
        // Check if mouse is still over the element
        if (!isElementUnderMouse(element)) {
          // Mouse is no longer over the element, hide tooltip immediately
          hideTooltip(element);
        } else {
          // Mouse is still over the element, update position
          const tooltipText = element.getAttribute('data-tooltip') || element.getAttribute('data-tooltip-temp');
          if (tooltipText) {
            showTooltip(element, tooltipText);
          }
        }
      }
    });
  }
  
  // Throttle scroll handling for better performance with chart loading protection
  let scrollTimeout;
  function handleScroll() {
    // Clear any existing timeout
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // Immediately check and hide tooltips that are no longer being hovered
    updateTooltipPositions();

    // Reduce scroll handling frequency during chart loading to prevent performance issues
    const isChartLoading = window.ViewportLazyLoader && window.ViewportLazyLoader.loadingComponents.size > 0;
    const scrollDelay = isChartLoading ? 32 : 16; // Slower during chart loading

    // Set a small timeout to handle rapid scroll events
    scrollTimeout = setTimeout(() => {
      updateTooltipPositions();
    }, scrollDelay);
  }
  
  // Initialize tooltip system
  initTooltipSystem();
  attachTooltipListeners();
  
  // Track mouse position globally
  document.addEventListener('mousemove', updateMousePosition, { passive: true });
  
  // Add scroll listeners to all scrollable containers
  function addScrollListeners() {
    // Window scroll
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Main content scroll
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      mainContent.addEventListener('scroll', handleScroll, { passive: true });
    }
    
    // Sales card scrolls
    const salesCards = document.querySelectorAll('.todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div');
    salesCards.forEach(card => {
      card.addEventListener('scroll', handleScroll, { passive: true });
    });
    
    // Any other scrollable containers
    const scrollableContainers = document.querySelectorAll('[style*="overflow-y: auto"], [style*="overflow: auto"]');
    scrollableContainers.forEach(container => {
      container.addEventListener('scroll', handleScroll, { passive: true });
    });
  }
  
  // Add scroll listeners
  addScrollListeners();

  // Hide any visible tooltips immediately when global suppression starts
  window.addEventListener('global:tooltips-hide', () => {
    activeTooltips.forEach((_, element) => hideTooltip(element));
  });

  // When suppression ends, if the user is still hovering the same element, show its tooltip immediately
  window.addEventListener('global:tooltips-allow', () => {
    if (!pendingHover) return;
    const { element, text } = pendingHover;
    // Only show if cursor is still over the element
    if (isElementUnderMouse(element)) {
      // Use temp attribute trick to avoid CSS tooltips (kept disabled anyway)
      element.removeAttribute('data-tooltip');
      element.setAttribute('data-tooltip-temp', text);
      showTooltip(element, text);
    }
    pendingHover = null;
  });
  
  // Update positioning on resize
  window.addEventListener('resize', updateTooltipPositions);
  
  // Re-initialize when components change (with cleanup tracking)
  const observer = window.EventCleanupManager.addMutationObserver(
    document.body,
    function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Re-attach listeners for new elements
          setTimeout(() => {
            attachTooltipListeners();
            addScrollListeners(); // Re-add scroll listeners for new scrollable containers
          }, 100);
        }
      });
    },
    {
      childList: true,
      subtree: true
    }
  );
  
  // Also re-initialize on component ready events (after render completion)
  window.addEventListener('componentReady', () => {
    setTimeout(() => {
      attachTooltipListeners();
      addScrollListeners(); // Re-add scroll listeners for new components
    }, 100);
  });
  
  // Expose global function to reinitialize tooltips
  window.reinitializeTooltips = function() {
    isInitialized = false;
    setTimeout(() => {
      initTooltipSystem();
      attachTooltipListeners();
      addScrollListeners(); // Re-add scroll listeners
    }, 50);
  };
  
  // Force reinitialize on component switches
  window.forceTooltipReinit = function() {
    isInitialized = false;
    // Clear active tooltips
    activeTooltips.clear();
    // Remove existing container
    const existingContainer = document.getElementById('global-tooltip-container');
    if (existingContainer) existingContainer.remove();
    
    setTimeout(() => {
      initTooltipSystem();
      attachTooltipListeners();
      addScrollListeners(); // Re-add scroll listeners
    }, 50);
  };
})();

// Function to handle navigation with debouncing
let navigationTimeout;
function handleNavigation(btn) {
    if (btn.classList.contains('locked') || btn.disabled) return;
    
    const componentLabel = btn.getAttribute('data-label');
    const componentName = componentMap[componentLabel];
    
    if (!componentName) {
        console.error(`No component mapping found for "${componentLabel}"`);
        return;
    }
    
    // Clear any pending navigation
    clearTimeout(navigationTimeout);
    
    // Debounce navigation to prevent rapid switching
    navigationTimeout = setTimeout(() => {
        // Update navigation state
        updateNavigation(btn);
        
        // Load and render component
        loadComponent(componentName);
        
        // Save current component to localStorage for persistence
        try {
            localStorage.setItem('currentComponent', componentName);
        } catch (error) {
            console.warn('Failed to save current component to localStorage:', error);
        }
        
        // Force tooltip system reinitialize after component load
        setTimeout(() => {
            if (window.forceTooltipReinit) {
                window.forceTooltipReinit();
            }
        }, 200);
    }, 100);
}

// Sidebar collapse/expand
function initializeSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const collapseBtn = document.querySelector('.sidebar-collapse-btn');
    
    if (!collapseBtn || !sidebar) {
        console.error('Sidebar elements not found');
        return;
    }

    // Apply collapsed class if needed based on localStorage
    try {
        if (localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('collapsed');
        }
    } catch (error) {
        console.warn('Failed to read sidebar state from localStorage:', error);
    }
    
    // Set aria-label based on current state
    const isCollapsed = sidebar.classList.contains('collapsed');
    collapseBtn.setAttribute('aria-label', isCollapsed ? 'Expand sidebar' : 'Collapse sidebar');
    // Set icon based on current state
    const iconImg = collapseBtn.querySelector('img.collapse-icon');
    if (iconImg) {
        iconImg.src = isCollapsed ? './assets/uncollapse-ic.svg' : './assets/collapse-ic.svg';
        iconImg.alt = isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar';
    }
    
    // Remove any existing listeners to prevent duplicates
    collapseBtn.removeEventListener('click', handleSidebarCollapse);
    
    // Add click handler with a named function reference that's defined outside
    collapseBtn.addEventListener('click', handleSidebarCollapse);
}

// Define the collapse handler outside the initializeSidebar function
// to avoid creating multiple function instances
function handleSidebarCollapse() {
    const sidebar = document.querySelector('.sidebar');
    const collapseBtn = document.querySelector('.sidebar-collapse-btn');

    if (!sidebar || !collapseBtn) return;

    // Check if we're transitioning currently - prevent rapid toggling
    if (sidebar.dataset.transitioning === 'true') {
        return;
    }

    // Check if heavy chart loading is in progress - prevent sidebar changes during performance-intensive operations
    if (window.ViewportLazyLoader && window.ViewportLazyLoader.loadingComponents.size > 0) {
        console.log('🚫 Sidebar toggle blocked: Chart loading in progress');
        return;
    }

    // Set transitioning flag with enhanced protection
    sidebar.dataset.transitioning = 'true';
    sidebar.dataset.transitionStartTime = Date.now();

    // Toggle collapsed class
    sidebar.classList.toggle('collapsed');
    const isNowCollapsed = sidebar.classList.contains('collapsed');

    // Update button aria-label
    collapseBtn.setAttribute('aria-label', isNowCollapsed ? 'Expand sidebar' : 'Collapse sidebar');
    // Update icon based on new state
    const iconImg = collapseBtn.querySelector('img.collapse-icon');
    if (iconImg) {
        iconImg.src = isNowCollapsed ? './assets/uncollapse-ic.svg' : './assets/collapse-ic.svg';
        iconImg.alt = isNowCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar';
    }

    // Save to localStorage
    try {
        localStorage.setItem('sidebarCollapsed', isNowCollapsed);
    } catch (error) {
        console.warn('Failed to save sidebar state to localStorage:', error);
    }

    // Get the main content element
    const mainContent = document.querySelector('.main-content');

    // Update main content margin with transition
    // Use exact sidebar widths - main content padding handles the visual spacing
    if (mainContent) {
        mainContent.style.transition = 'margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        mainContent.style.marginLeft = isNowCollapsed ? '116px' : '310px'; // Exact sidebar widths
    }

    // Force reflow to ensure the transition applies properly
    sidebar.offsetHeight;

    // Also make sure text visibility is properly handled
    const sidebarBtns = document.querySelectorAll('.sidebar-btn');
    sidebarBtns.forEach(btn => {
        const span = btn.querySelector('span');
        if (span) {
            if (isNowCollapsed) {
                // Fade out text
                span.style.opacity = '0';
                span.style.pointerEvents = 'none';
                // After transition, hide with display:none
                setTimeout(() => {
                    span.style.display = 'none';
                }, 300);
            } else {
                // Show text, then fade in
                span.style.display = 'inline';
                setTimeout(() => {
                    span.style.opacity = '1';
                    span.style.pointerEvents = '';
                }, 10); // allow reflow
            }
        }
    });

    // Enhanced transitioning flag reset with performance monitoring
    const resetTransitionFlag = () => {
        if (sidebar.dataset.transitioning === 'true') {
            sidebar.dataset.transitioning = 'false';
            delete sidebar.dataset.transitionStartTime;
            console.log('✅ Sidebar transition completed');
        }
    };

    // Use both setTimeout and transitionend for robust flag reset
    const transitionTimeout = setTimeout(resetTransitionFlag, 350); // Slightly longer than CSS transition

    // Listen for actual transition end (more reliable during heavy operations)
    const handleTransitionEnd = (event) => {
        if (event.target === sidebar && event.propertyName === 'width') {
            clearTimeout(transitionTimeout);
            resetTransitionFlag();
            sidebar.removeEventListener('transitionend', handleTransitionEnd);
        }
    };

    sidebar.addEventListener('transitionend', handleTransitionEnd);
}

// Dynamic Dashboard Header Height Calculator
function updateDashboardHeaderHeight() {
    const dashboardHeader = document.querySelector('.dashboard-header');

    if (!dashboardHeader) {
        return; // Header doesn't exist yet, will be calculated when dashboard loads
    }

    // Force a reflow to ensure accurate height measurement
    dashboardHeader.offsetHeight;

    // Get the actual computed height of the dashboard header
    const headerHeight = dashboardHeader.offsetHeight;

    // Set the CSS custom property on the root element smoothly
    document.documentElement.style.setProperty('--dashboard-header-height', `${headerHeight}px`);

    console.log(`Dashboard header height updated: ${headerHeight}px`);
}

// Initialize dashboard header height monitoring
function initializeDashboardHeaderHeight() {
    // Initial calculation
    updateDashboardHeaderHeight();

    // Track previous window width to only update on width changes
    let previousWidth = window.innerWidth;

    // Update only on window width changes (responsive breakpoints)
    window.addEventListener('resize', () => {
        const currentWidth = window.innerWidth;

        // Only update if width actually changed (not just height)
        if (currentWidth !== previousWidth) {
            updateDashboardHeaderHeight();
            previousWidth = currentWidth;
        }
    });

    // Update when sidebar state changes (collapse/expand)
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        // Monitor sidebar class changes for collapse/expand
        const sidebarObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    // Skip updates if chart loading is in progress to prevent conflicts
                    if (window.ViewportLazyLoader && window.ViewportLazyLoader.loadingComponents.size > 0) {
                        console.log('⏸️ Skipping header height update during chart loading');
                        return;
                    }

                    // Sidebar collapsed/expanded - update header height after transition
                    setTimeout(() => {
                        updateDashboardHeaderHeight();
                    }, 320); // Slightly after the 300ms sidebar transition
                }
            });
        });

        sidebarObserver.observe(sidebar, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    // Only update when dashboard component is ready (after render completion)
    window.addEventListener('componentReady', (event) => {
        // Only for dashboard component
        if (event.detail && event.detail.name === 'dashboard') {
            setTimeout(() => {
                updateDashboardHeaderHeight();
            }, 100);
        }
    });
}

// Initialize UI with optimized event handling
function initializeUI() {
    // Initialize checkboxes
    initializeCheckboxes();
    
    // Theme management
    const themeToggleBtn = document.querySelector('.theme-toggle-btn');
    const toggles = themeToggleBtn?.querySelectorAll('.toggle');
    
    // Get current theme (already set in HTML)
    const currentTheme = document.documentElement.getAttribute('data-theme');
    
    // Set initial toggle state
    if (toggles) {
        toggles.forEach(toggle => {
            const isLight = toggle.querySelector('img[alt="Light mode"]');
            const isDark = toggle.querySelector('img[alt="Dark mode"]');
            toggle.classList.toggle('active', 
                (currentTheme === 'light' && isLight) ||
                (currentTheme === 'dark' && isDark)
            );
        });
    }
    
    // Theme toggle handler with debouncing
    let themeTimeout;
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', () => {
            clearTimeout(themeTimeout);
            themeTimeout = setTimeout(() => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                // Add transitioning class
                document.documentElement.classList.add('theme-transitioning');
                
                // Apply new theme
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                // Update toggle states
                toggles?.forEach(toggle => {
                    const isLight = toggle.querySelector('img[alt="Light mode"]');
                    const isDark = toggle.querySelector('img[alt="Dark mode"]');
                    toggle.classList.toggle('active', 
                        (newTheme === 'light' && isLight) ||
                        (newTheme === 'dark' && isDark)
                    );
                });

                // Remove transitioning class after transition completes
                setTimeout(() => {
                    document.documentElement.classList.remove('theme-transitioning');
                }, 300); // Match this with your CSS transition duration
            }, 0); // Removed the delay to make it more responsive
        });
    }

    // Navigation setup with event delegation
    const sidebar = document.querySelector('.sidebar-nav');
    if (sidebar) {
        sidebar.addEventListener('click', (e) => {
            const btn = e.target.closest('.sidebar-btn');
            if (btn) {
                handleNavigation(btn);
            }
        });
    }

    // Initialize sidebar state
    initializeSidebar();

    // Initialize dashboard header height monitoring
    initializeDashboardHeaderHeight();

    // Load initial component
    const lastComponent = localStorage.getItem('currentComponent') || 'dashboard';
    const componentKey = Object.keys(componentMap).find(
        key => componentMap[key] === lastComponent
    );

    if (componentKey) {
        const activeButton = document.querySelector(`.sidebar-btn[data-label="${componentKey}"]`);
        if (activeButton) {
            updateNavigation(activeButton);
            // Set initial current component before loading
            currentComponent = null; // Will be set properly in loadComponent
            loadComponent(lastComponent);
        }
    }

    // Set initial tooltip text for theme toggle
    function updateThemeToggleTooltip() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        if (themeToggleBtn) {
            themeToggleBtn.setAttribute('data-tooltip', currentTheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode');
        }
    }
    updateThemeToggleTooltip();
    // Update tooltip on theme change
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('mouseenter', updateThemeToggleTooltip);
        themeToggleBtn.addEventListener('focus', updateThemeToggleTooltip);
    }
    // Also update after theme is toggled
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', () => {
            setTimeout(updateThemeToggleTooltip, 10); // Wait for theme to update
        });
    }
}

// Component Loader - loads JS for a component
function loadComponent(name) {
    console.log(`Loading component: ${name}`);
    console.log(`Looking for file: ./components/${name}/${name}.js`);

    // Dispatch componentUnloaded event for the previous component if one exists
    if (currentComponent && currentComponent !== name) {
        console.log(`Dispatching componentUnloaded event for: ${currentComponent}`);
        window.dispatchEvent(new CustomEvent('componentUnloaded', {
            detail: { component: currentComponent }
        }));
    }

    // Show loading state in content area and viewport-centered overlay for dashboard
    mainContent.innerHTML = '<div class="loading">Loading...</div>';
    if (name === 'dashboard' && window.SnapLoader) {
        window.SnapLoader.showOverlay(mainContent, {
            text: 'Loading dashboard...',
            id: 'component-loader',
            viewport: true
        });
    }
    
    // MODIFIED: Load the combined JS file for the component
    const script = document.createElement('script');
    const scriptPath = `./components/${name}/${name}.js`;
    script.src = scriptPath;
    script.setAttribute('data-component', name);
    
    console.log(`Created script element with src: ${scriptPath}`);
    
    script.onload = function() {
        console.log(`Component ${name} script loaded successfully`);
        
        // Explicitly call the component's render function after script loads
        // Component should be available on window with the naming pattern: window.componentNameComponent
        const componentName = name.replace(/-([a-z])/g, (g) => g[1].toUpperCase()) + 'Component';
        console.log(`Looking for component object: ${componentName}`);
        
        if (window[componentName] && typeof window[componentName].render === 'function') {
            console.log(`Found ${componentName}.render function, calling it now`);
            window[componentName].render();

            // Update current component tracking
            currentComponent = name;

            if (name === 'dashboard' && window.SnapLoader) {
                window.SnapLoader.hideOverlay(mainContent);
            }

            // Force reinitialize tooltips after component renders
            setTimeout(() => {
                if (window.forceTooltipReinit) {
                    console.log(`Reinitializing tooltips for component: ${name}`);
                    window.forceTooltipReinit();
                }

                // Specifically reinitialize custom tooltips for dashboard component
                if (name === 'dashboard' && window.initializeCustomTooltips) {
                    console.log(`Reinitializing custom dashboard tooltips...`);
                    window.initializeCustomTooltips();
                }

                // Update dashboard header height when dashboard component loads
                if (name === 'dashboard') {
                    updateDashboardHeaderHeight();
                }

                // Dispatch componentReady event after render and initialization is complete
                console.log(`Dispatching componentReady event for: ${name}`);
                window.dispatchEvent(new CustomEvent('componentReady', {
                    detail: { name }
                }));
            }, 200);
        } else {
            console.error(`Component ${name} loaded but ${componentName}.render function not found`);
            mainContent.innerHTML = `
                <div class="error-container">
                    <h1>Component Error</h1>
                    <p>Component loaded but render function not found</p>
                    <p>Expected: window.${componentName}.render</p>
                </div>
            `;
            if (name === 'dashboard' && window.SnapLoader) {
                window.SnapLoader.hideOverlay(mainContent);
            }

            // Still update current component and dispatch ready event for error case
            currentComponent = name;
            window.dispatchEvent(new CustomEvent('componentReady', {
                detail: { name, error: 'render_function_not_found' }
            }));
        }
    };
    
    script.onerror = function(error) {
        console.error(`Error loading component ${name} from ${scriptPath}:`, error);
        mainContent.innerHTML = `
            <div class="error-container">
                <h1>Error Loading Component</h1>
                <p>Failed to load ${name} component from ${scriptPath}</p>
                <p>Check browser console for details</p>
            </div>
        `;
        if (name === 'dashboard' && window.SnapLoader) {
            window.SnapLoader.hideOverlay(mainContent);
        }

        // Still update current component and dispatch ready event for error case
        currentComponent = name;
        window.dispatchEvent(new CustomEvent('componentReady', {
            detail: { name, error: 'script_load_failed' }
        }));
    };
    
    // Remove any previous component scripts
    const oldScript = document.querySelector(`script[data-component]`);
    if (oldScript) {
        console.log(`Removing old script: ${oldScript.src}`);
        oldScript.remove();
    }
    
    // Add the new script
    document.body.appendChild(script);
    console.log(`Appended script to body: ${scriptPath}`);

    // Note: componentReady event will be dispatched after render completion in script.onload
}

// Apply theme from localStorage immediately before page renders
function applyInitialTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    // Apply initial sidebar collapsed state if needed
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    
    if (isCollapsed) {
        // Apply inline style to prevent transition animation during initial load
        const style = document.createElement('style');
        style.id = 'initial-sidebar-style';
        style.textContent = `
            .sidebar {
                width: 116px !important;
                min-width: 116px !important;
            }
            .sidebar-inner {
                width: 60px !important;
            }
            .sidebar * {
                transition: none !important;
            }
            .main-content {
                margin-left: 116px !important;
            }
        `;
        document.head.appendChild(style);
        
        // Set a flag to remove the style after DOM is ready
        window.removeInitialStyle = true;
    }
}

// Create and append the initial HTML structure
function createAppStructure() {
    // Preserve existing elements like tooltip containers before replacing body content
    const existingTooltipContainer = document.getElementById('global-tooltip-container');

    // Set the app container HTML
    document.body.innerHTML = snapAppHTML;

    // Re-add preserved elements if they existed
    if (existingTooltipContainer) {
        document.body.appendChild(existingTooltipContainer);
    }
    
    // Get reference to main content area
    mainContent = document.querySelector('.main-content');
    
    // Apply collapsed class if needed
    if (localStorage.getItem('sidebarCollapsed') === 'true') {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.add('collapsed');
        }
        
        // Remove initial style to allow transitions after DOM is ready
        if (window.removeInitialStyle) {
            setTimeout(() => {
                const initialStyle = document.getElementById('initial-sidebar-style');
                if (initialStyle) {
                    initialStyle.remove();
                }
                window.removeInitialStyle = false;
            }, 100);
        }
    }
}

// Apply initial theme immediately
applyInitialTheme();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        createAppStructure();
        // Defer UI init so loader system is defined before first component load
        setTimeout(() => initializeUI(), 0);
    });
} else {
    createAppStructure();
    // Defer UI init so loader system is defined before first component load
    setTimeout(() => initializeUI(), 0);
}

// ============================================================================
// GLOBAL LOADER SYSTEM
// ============================================================================

/**
 * Global Loader Utility - Provides consistent loading states across the app
 * Based on patterns from loader ref code.js with theme integration
 */
window.SnapLoader = {
  // Active loaders tracking
  activeLoaders: new Map(),
  
  // Default configuration
  defaults: {
    overlay: {
      text: 'Loading...',
      size: 'medium',
      state: 'default', // default, success, error, warning
      viewport: false
    },
    inline: {
      position: 'right',
      size: 'small'
    }
  },

  /**
   * Show overlay loader - Full container coverage with spinner and text
   * @param {HTMLElement} container - Container element to overlay
   * @param {Object} options - Configuration options
   * @param {string} options.text - Loading text to display
   * @param {string} options.state - Loader state (default, success, error, warning)
   * @param {string} options.size - Spinner size (small, medium, large)
   * @param {string} options.id - Unique identifier for this loader
   * @returns {HTMLElement} - The created loader element
   */
  showOverlay(container, options = {}) {
    if (!container) {
      console.warn('SnapLoader.showOverlay: Container element is required');
      return null;
    }

    const config = { ...this.defaults.overlay, ...options };
    const loaderId = config.id || `overlay-${Date.now()}`;
    
    // Remove existing loader if present
    this.hideOverlay(container);
    
    // Ensure container has relative positioning when using container overlay
    if (!config.viewport && getComputedStyle(container).position === 'static') {
      container.style.position = 'relative';
    }
    
    // Create loader overlay
    const loaderOverlay = document.createElement('div');
    loaderOverlay.className = `snap-loader-overlay ${config.state !== 'default' ? `snap-loader-${config.state}` : ''}`;
    if (config.viewport) {
      loaderOverlay.classList.add('snap-loader-viewport');
    }
    loaderOverlay.id = loaderId;
    loaderOverlay.setAttribute('aria-label', `Loading: ${config.text}`);
    loaderOverlay.setAttribute('role', 'status');
    loaderOverlay.setAttribute('aria-live', 'polite');
    
    // Create spinner
    const spinner = document.createElement('div');
    spinner.className = 'snap-loader-spinner';
    
    // Create text element
    const textElement = document.createElement('div');
    textElement.className = 'snap-loader-text';
    textElement.textContent = config.text;
    
    // Assemble loader
    loaderOverlay.appendChild(spinner);
    loaderOverlay.appendChild(textElement);
    if (config.viewport) {
      document.body.appendChild(loaderOverlay);
    } else {
      // Insert as first child to ensure it never affects layout order
      try {
        container.insertBefore(loaderOverlay, container.firstChild || null);
      } catch (_) {
        // Fallback to append if insertBefore fails for any reason
        container.appendChild(loaderOverlay);
      }
    }

    // Add class to container to disable hover states (fallback for browsers without :has() support)
    container.classList.add('snap-loader-active');

    // Track active loader
    this.activeLoaders.set(container, {
      type: 'overlay',
      element: loaderOverlay,
      id: loaderId
    });

    return loaderOverlay;
  },

  /**
   * Hide overlay loader
   * @param {HTMLElement} container - Container element with overlay
   * @param {Function} callback - Optional callback after hide animation
   */
  hideOverlay(container, callback = null) {
    if (!container) return;
    
    const loaderInfo = this.activeLoaders.get(container);
    if (!loaderInfo || loaderInfo.type !== 'overlay') return;
    
    const loader = loaderInfo.element;
    if (!loader) return;
    
    // Smooth fade out
    loader.style.opacity = '0';
    loader.style.transition = 'opacity 0.3s ease';
    
    setTimeout(() => {
      if (loader.parentNode) {
        loader.parentNode.removeChild(loader);
      }
      // Remove class from container to re-enable hover states
      container.classList.remove('snap-loader-active');
      this.activeLoaders.delete(container);
      if (callback) callback();
    }, 300);
  },

  /**
   * Show inline loader - Small loader for inputs, buttons, etc.
   * @param {HTMLElement} element - Element to add loader to
   * @param {Object} options - Configuration options
   * @param {string} options.position - Loader position (right, left, center)
   * @param {string} options.size - Loader size (small, medium)
   * @param {string} options.state - Loader state
   * @returns {HTMLElement} - The created loader element
   */
  showInline(element, options = {}) {
    if (!element) {
      console.warn('SnapLoader.showInline: Element is required');
      return null;
    }

    const config = { ...this.defaults.inline, ...options };
    const loaderId = `inline-${Date.now()}`;
    
    // Remove existing inline loader
    this.hideInline(element);
    
    // Ensure element has relative positioning
    if (getComputedStyle(element).position === 'static') {
      element.style.position = 'relative';
    }
    
    // Create inline loader
    const inlineLoader = document.createElement('div');
    inlineLoader.className = `snap-loader-inline ${config.position === 'input' ? 'input-loader' : ''} ${config.state !== 'default' ? `snap-loader-${config.state}` : ''}`;
    inlineLoader.id = loaderId;
    inlineLoader.style.display = 'block';
    inlineLoader.setAttribute('aria-label', 'Loading');
    
    // Position based on config
    if (config.position === 'right') {
      inlineLoader.style.right = '12px';
      inlineLoader.style.top = '50%';
      inlineLoader.style.transform = 'translateY(-50%)';
    } else if (config.position === 'left') {
      inlineLoader.style.left = '12px';
      inlineLoader.style.top = '50%';
      inlineLoader.style.transform = 'translateY(-50%)';
    } else if (config.position === 'center') {
      inlineLoader.style.left = '50%';
      inlineLoader.style.top = '50%';
      inlineLoader.style.transform = 'translate(-50%, -50%)';
    }
    
    element.appendChild(inlineLoader);
    
    // Track active loader
    this.activeLoaders.set(element, {
      type: 'inline',
      element: inlineLoader,
      id: loaderId
    });
    
    return inlineLoader;
  },

  /**
   * Hide inline loader
   * @param {HTMLElement} element - Element with inline loader
   */
  hideInline(element) {
    if (!element) return;
    
    const loaderInfo = this.activeLoaders.get(element);
    if (!loaderInfo || loaderInfo.type !== 'inline') return;
    
    const loader = loaderInfo.element;
    if (loader && loader.parentNode) {
      loader.parentNode.removeChild(loader);
    }
    
    this.activeLoaders.delete(element);
  },

  /**
   * Show button loading state
   * @param {HTMLElement} button - Button element
   * @param {Object} options - Configuration options
   * @param {string} options.text - Optional loading text
   */
  showButtonLoading(button, options = {}) {
    if (!button) return;
    
    // Store original button content
    if (!button.dataset.originalContent) {
      button.dataset.originalContent = button.innerHTML;
    }
    
    // Add loading class
    button.classList.add('snap-loader-button-loading');
    button.disabled = true;
    
    // Update button text if provided
    if (options.text) {
      button.innerHTML = options.text;
    }
    
    // Track active loader
    this.activeLoaders.set(button, {
      type: 'button',
      originalContent: button.dataset.originalContent
    });
  },

  /**
   * Hide button loading state
   * @param {HTMLElement} button - Button element
   */
  hideButtonLoading(button) {
    if (!button) return;
    
    const loaderInfo = this.activeLoaders.get(button);
    if (!loaderInfo || loaderInfo.type !== 'button') return;
    
    // Remove loading class
    button.classList.remove('snap-loader-button-loading');
    button.disabled = false;
    
    // Restore original content
    if (button.dataset.originalContent) {
      button.innerHTML = button.dataset.originalContent;
      delete button.dataset.originalContent;
    }
    
    this.activeLoaders.delete(button);
  },

  /**
   * Create standalone loader element
   * @param {string} size - Loader size (small, medium, large)
   * @param {string} state - Loader state (default, success, error, warning)
   * @returns {HTMLElement} - Standalone loader element
   */
  createStandalone(size = 'medium', state = 'default') {
    const loader = document.createElement('div');
    loader.className = `snap-loader-${size} ${state !== 'default' ? `snap-loader-${state}` : ''}`;
    loader.setAttribute('aria-label', 'Loading');
    loader.setAttribute('role', 'status');
    return loader;
  },

  /**
   * Show loading state for Sales Card
   * @param {HTMLElement} salesCard - Sales card element
   * @param {string} text - Loading text
   */
  showSalesCardLoading(salesCard, text = 'Loading sales data...') {
    if (!salesCard) return;
    
    salesCard.classList.add('loading');
    return this.showOverlay(salesCard, { text, id: 'sales-card-loader' });
  },

  /**
   * Hide loading state for Sales Card
   * @param {HTMLElement} salesCard - Sales card element
   */
  hideSalesCardLoading(salesCard) {
    if (!salesCard) return;
    
    salesCard.classList.remove('loading');
    this.hideOverlay(salesCard);
  },

  /**
   * Show loading state for sidebar button
   * @param {HTMLElement} button - Sidebar button element
   */
  showSidebarButtonLoading(button) {
    if (!button) return;
    
    button.classList.add('loading');
    this.activeLoaders.set(button, { type: 'sidebar-button' });
  },

  /**
   * Hide loading state for sidebar button
   * @param {HTMLElement} button - Sidebar button element
   */
  hideSidebarButtonLoading(button) {
    if (!button) return;
    
    button.classList.remove('loading');
    this.activeLoaders.delete(button);
  },

  /**
   * Update loader text
   * @param {HTMLElement} container - Container with loader
   * @param {string} newText - New text to display
   */
  updateText(container, newText) {
    const loaderInfo = this.activeLoaders.get(container);
    if (!loaderInfo || loaderInfo.type !== 'overlay') return;
    
    const textElement = loaderInfo.element.querySelector('.snap-loader-text');
    if (textElement) {
      textElement.textContent = newText;
    }
  },

  /**
   * Update loader state (changes color)
   * @param {HTMLElement} container - Container with loader
   * @param {string} newState - New state (success, error, warning, default)
   */
  updateState(container, newState) {
    const loaderInfo = this.activeLoaders.get(container);
    if (!loaderInfo) return;
    
    const loader = loaderInfo.element;
    if (!loader) return;
    
    // Remove existing state classes
    loader.classList.remove('snap-loader-success', 'snap-loader-error', 'snap-loader-warning');
    
    // Add new state class
    if (newState !== 'default') {
      loader.classList.add(`snap-loader-${newState}`);
    }
  },

  /**
   * Hide all active loaders
   */
  hideAll() {
    this.activeLoaders.forEach((loaderInfo, element) => {
      switch (loaderInfo.type) {
        case 'overlay':
          this.hideOverlay(element);
          break;
        case 'inline':
          this.hideInline(element);
          break;
        case 'button':
          this.hideButtonLoading(element);
          break;
        case 'sidebar-button':
          this.hideSidebarButtonLoading(element);
          break;
      }
    });
  },

  /**
   * Check if element has active loader
   * @param {HTMLElement} element - Element to check
   * @returns {boolean} - True if element has active loader
   */
  isLoading(element) {
    return this.activeLoaders.has(element);
  },

  /**
   * Get active loader info
   * @param {HTMLElement} element - Element to check
   * @returns {Object|null} - Loader info or null
   */
  getLoaderInfo(element) {
    return this.activeLoaders.get(element) || null;
  }
};

// ============================================================================
// GLOBAL LOADER CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Quick access functions for common loader operations
 */

// Show overlay loader with default settings
window.showLoader = (container, text = 'Loading...') => {
  return window.SnapLoader.showOverlay(container, { text });
};

// Hide overlay loader
window.hideLoader = (container) => {
  window.SnapLoader.hideOverlay(container);
};

// Show inline loader
window.showInlineLoader = (element, position = 'right') => {
  return window.SnapLoader.showInline(element, { position });
};

// Hide inline loader
window.hideInlineLoader = (element) => {
  window.SnapLoader.hideInline(element);
};

// Show button loading
window.showButtonLoader = (button, text = null) => {
  window.SnapLoader.showButtonLoading(button, { text });
};

// Hide button loading
window.hideButtonLoader = (button) => {
  window.SnapLoader.hideButtonLoading(button);
};

// ============================================================================
// INTEGRATION WITH EXISTING COMPONENT LOADER
// ============================================================================

// Enhance existing loadComponent function with loader
const originalLoadComponent = window.loadComponent;
if (originalLoadComponent) {
  window.loadComponent = function(name) {
    // Delegate to original; dashboard overlay is managed inside loadComponent
    return originalLoadComponent.call(this, name);
  };
}

// ============================================================================
// CLEANUP ON PAGE UNLOAD
// ============================================================================

window.addEventListener('beforeunload', () => {
  window.SnapLoader.hideAll();
}); 