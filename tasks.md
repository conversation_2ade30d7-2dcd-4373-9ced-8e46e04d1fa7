# Current Task: Make Listings Status Overview loader horizontal (spinner left, text right)

## Context7 + Sequential Thinking
- Context: The overlay loader is created via `window.SnapLoader.showOverlay(container, options)` in `snapapp.js`, which inserts `.snap-loader-overlay` with a spinner and text stacked vertically by default. The Listings Status Overview section has reduced height, so the loader needs a horizontal layout within `.listings-status-overview`.
- Intent: Improve readability and spacing by placing spinner on the left and text on the right when the overlay targets `.listings-status-overview`.
- Dependencies: CSS in `snapapp.css` for `.snap-loader-overlay`; JS calls already exist in `components/dashboard/dashboard.js` using `window.SnapLoader.showOverlay(listingsStatusOverview, {...})`.

## Discovery Documentation (Search Phase)
- Found loader system in `snapapp.js` and default overlay styles in `snapapp.css` (vertical column layout).
- Located the Listings Status Overview loader call at `components/dashboard/dashboard.js` lines ~7330.

## Existing Functionality
- DOM generation: Loader overlay created in JS; styles handled via CSS.
- CSS injection: Central stylesheet `snapapp.css`.
- Chrome APIs: N/A for this change.
- Events: N/A.

## Gaps Identified
- No context-specific style to lay out overlay horizontally for `.listings-status-overview`.

## Proposed Approach
- Adjust CSS: add a selector `.listings-status-overview > .snap-loader-overlay` to set `flex-direction: row`, compact padding, and smaller spinner and text size.
- No JS changes needed; existing calls will pick up new styles when the overlay is appended.

## Tasks
- [x] Add horizontal layout CSS for loader scoped to `.listings-status-overview`.
- [ ] Manual verify in Dashboard: trigger refresh and confirm spinner/text alignment and sizing.

## Implementation Tracking
- CSS: Added horizontal loader rules under `.listings-status-overview > .snap-loader-overlay` in `snapapp.css`.

## Testing Strategy
- Trigger Listings Status Overview refresh and ensure loader shows spinner left and text right without vertical crowding.
- Check dark mode contrast and ensure padding does not overflow container.

## Linter & Error Verification
- Ran lints on `snapapp.css`; unrelated pre-existing warnings remain; no new errors introduced by these edits.
